<template>
  <div class="relative flex items-center justify-center h-80">
    <!-- ECharts 雷达图容器 -->
    <div ref="chartContainer" class="w-full h-full"></div>

    <!-- 中心头像 -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div
           class="flex items-center justify-center w-16 h-16 border-2 border-white shadow-lg bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl">
        <span class="text-lg font-bold text-white">{{ username.charAt(0).toUpperCase() }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props 定义
interface Props {
  username: string
  data?: number[]
  labels?: string[]
  maxValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [9.5, 8.5, 8.5, 9.5, 8.5, 8.5],
  labels: () => ['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'],
  maxValue: 10
})

// 响应式引用
const chartContainer = ref<HTMLElement>()
let chartInstance: any = null

// 动态导入 ECharts
const initChart = async () => {
  try {
    // 动态导入 ECharts
    const echarts = await import('echarts')

    if (!chartContainer.value) return

    // 初始化图表实例
    chartInstance = echarts.init(chartContainer.value)

    // 配置选项
    const option = {
      backgroundColor: 'transparent',
      radar: {
        // 雷达图配置
        shape: 'polygon', // 多边形
        splitNumber: 3, // 分割层数
        radius: '70%',
        center: ['50%', '50%'],

        // 坐标轴配置
        axisLine: {
          lineStyle: {
            color: '#e2e8f0',
            width: 1,
            opacity: 0.4
          }
        },

        // 分割线配置
        splitLine: {
          lineStyle: {
            color: '#e2e8f0',
            width: 1,
            opacity: 0.4
          }
        },

        // 分割区域配置
        splitArea: {
          show: false
        },

        // 指示器配置
        indicator: props.labels.map(() => ({
          name: '', // 不显示标签，我们用自定义的SCAI标签
          max: props.maxValue,
          axisLabel: {
            show: false
          }
        })),

        // 轴标签配置
        axisLabel: {
          show: false
        }
      },

      // 系列数据
      series: [{
        type: 'radar',
        symbol: 'circle',
        symbolSize: 8,

        // 数据配置
        data: [{
          value: props.data,

          // 区域样式
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(96, 165, 250, 0.6)' },
                { offset: 1, color: 'rgba(59, 130, 246, 0.8)' }
              ]
            }
          },

          // 线条样式
          lineStyle: {
            color: '#3b82f6',
            width: 2
          },

          // 数据点样式
          itemStyle: {
            color: '#1d4ed8',
            borderColor: '#ffffff',
            borderWidth: 2
          }
        }],

        // 动画配置
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }],

      // 全局动画配置
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }

    // 设置配置并渲染
    chartInstance.setOption(option)

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chartInstance?.resize()
    })
    resizeObserver.observe(chartContainer.value)

    // 组件卸载时清理
    onUnmounted(() => {
      resizeObserver.disconnect()
      chartInstance?.dispose()
    })

  } catch (error) {
    console.error('Failed to load ECharts:', error)
    // 如果 ECharts 加载失败，回退到静态 SVG
    await fallbackToStaticSVG()
  }
}

// 回退到静态 SVG 的方法
const fallbackToStaticSVG = async () => {
  if (!chartContainer.value) return

  // 创建静态 SVG 作为回退方案
  chartContainer.value.innerHTML = `
    <svg class="w-full h-full" viewBox="0 0 300 300">
      <!-- 定义渐变 -->
      <defs>
        <linearGradient id="radarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.8" />
        </linearGradient>
      </defs>

      <!-- 背景六边形网格 -->
      <g stroke="#e2e8f0" stroke-width="1" fill="none" opacity="0.4">
        <polygon points="150,50 220,100 220,200 150,250 80,200 80,100" />
        <polygon points="150,80 190,115 190,185 150,220 110,185 110,115" />
        <polygon points="150,110 160,130 160,170 150,190 140,170 140,130" />
        <line x1="150" y1="150" x2="150" y2="50" />
        <line x1="150" y1="150" x2="220" y2="100" />
        <line x1="150" y1="150" x2="220" y2="200" />
        <line x1="150" y1="150" x2="150" y2="250" />
        <line x1="150" y1="150" x2="80" y2="200" />
        <line x1="150" y1="150" x2="80" y2="100" />
      </g>

      <!-- 数据区域 -->
      <polygon points="150,70 200,110 200,190 150,230 100,190 100,110"
               fill="url(#radarGradient)"
               stroke="#3b82f6"
               stroke-width="2" />

      <!-- 数据点 -->
      <circle cx="150" cy="70" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="200" cy="110" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="200" cy="190" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="150" cy="230" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="100" cy="190" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="100" cy="110" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
    </svg>
  `
}

// 更新图表数据的方法
const updateChart = () => {
  if (!chartInstance) return

  const option = {
    radar: {
      indicator: props.labels.map(() => ({
        name: '',
        max: props.maxValue
      }))
    },
    series: [{
      data: [{
        value: props.data
      }]
    }]
  }

  chartInstance.setOption(option, { replaceMerge: ['series'] })
}

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(() => props.labels, updateChart, { deep: true })
watch(() => props.maxValue, updateChart)

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  await initChart()
})
</script>
