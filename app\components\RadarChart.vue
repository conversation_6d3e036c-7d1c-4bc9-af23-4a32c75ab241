<template>
  <div class="relative flex items-center justify-center h-80">
    <!-- ECharts 雷达图容器 -->
    <div ref="chartContainer" class="w-full h-full"></div>

    <!-- 中心图片 -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div
           class="flex items-center justify-center w-20 h-20 overflow-hidden bg-white border-white rounded-full shadow-xl border-3">
        <img
             src="@/assets/images/logo.png"
             alt="Profile"
             class="object-cover w-full h-full"
             @error="showFallback = true"
             v-if="!showFallback">
        <!-- 图片加载失败时的回退显示 -->
        <div
             v-else
             class="flex items-center justify-center w-full h-full bg-gradient-to-br from-blue-500 to-blue-600">
          <span class="text-xl font-bold text-white">{{ username.charAt(0).toUpperCase() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props 定义
interface Props {
  username: string
  data?: number[]
  labels?: string[]
  maxValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [9.5, 8.5, 8.5, 9.5, 8.5],
  labels: () => ['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'],
  maxValue: 10
})

// 响应式引用
const chartContainer = ref<HTMLElement>()
const showFallback = ref(false)
let chartInstance: any = null

// 动态导入 ECharts
const initChart = async () => {
  try {
    // 动态导入 ECharts
    const echarts = await import('echarts')

    if (!chartContainer.value) return

    // 初始化图表实例
    chartInstance = echarts.init(chartContainer.value)

    // 配置选项
    const option = {
      backgroundColor: 'transparent',
      radar: {
        // 雷达图配置 - 五芒星形状
        shape: 'polygon', // 多边形
        splitNumber: 3, // 分割层数
        radius: '75%',
        center: ['50%', '50%'],
        startAngle: 90, // 从顶部开始

        // 坐标轴配置
        axisLine: {
          lineStyle: {
            color: '#e2e8f0',
            width: 1.5,
            opacity: 0.5
          }
        },

        // 分割线配置
        splitLine: {
          lineStyle: {
            color: '#e2e8f0',
            width: 1,
            opacity: 0.4
          }
        },

        // 分割区域配置
        splitArea: {
          show: false
        },

        // 指示器配置 - 5个点对应五芒星
        indicator: props.labels.map(() => ({
          name: '', // 不显示标签，我们用自定义的SCAI标签
          max: props.maxValue,
          axisLabel: {
            show: false
          }
        })),

        // 轴标签配置
        axisLabel: {
          show: false
        }
      },

      // 系列数据
      series: [{
        type: 'radar',
        symbol: 'circle',
        symbolSize: 8,

        // 数据配置
        data: [{
          value: props.data,

          // 区域样式
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(96, 165, 250, 0.6)' },
                { offset: 1, color: 'rgba(59, 130, 246, 0.8)' }
              ]
            }
          },

          // 线条样式
          lineStyle: {
            color: '#3b82f6',
            width: 2
          },

          // 数据点样式
          itemStyle: {
            color: '#1d4ed8',
            borderColor: '#ffffff',
            borderWidth: 2
          }
        }],

        // 动画配置
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }],

      // 全局动画配置
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }

    // 设置配置并渲染
    chartInstance.setOption(option)

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chartInstance?.resize()
    })
    resizeObserver.observe(chartContainer.value)

    // 组件卸载时清理
    onUnmounted(() => {
      resizeObserver.disconnect()
      chartInstance?.dispose()
    })

  } catch (error) {
    console.error('Failed to load ECharts:', error)
    // 如果 ECharts 加载失败，回退到静态 SVG
    await fallbackToStaticSVG()
  }
}

// 回退到静态 SVG 的方法 - 五芒星形状
const fallbackToStaticSVG = async () => {
  if (!chartContainer.value) return

  // 创建静态 SVG 作为回退方案 - 五芒星形状
  chartContainer.value.innerHTML = `
    <svg class="w-full h-full" viewBox="0 0 300 300">
      <!-- 定义渐变 -->
      <defs>
        <linearGradient id="radarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.8" />
        </linearGradient>
      </defs>

      <!-- 背景五芒星网格 -->
      <g stroke="#e2e8f0" stroke-width="1" fill="none" opacity="0.4">
        <!-- 外层五芒星 -->
        <polygon points="150,40 210,110 190,190 110,190 90,110" />
        <!-- 中层五芒星 -->
        <polygon points="150,70 185,125 175,175 125,175 115,125" />
        <!-- 内层五芒星 -->
        <polygon points="150,100 170,140 160,160 140,160 130,140" />

        <!-- 从中心到各顶点的连线 -->
        <line x1="150" y1="150" x2="150" y2="40" />
        <line x1="150" y1="150" x2="210" y2="110" />
        <line x1="150" y1="150" x2="190" y2="190" />
        <line x1="150" y1="150" x2="110" y2="190" />
        <line x1="150" y1="150" x2="90" y2="110" />
      </g>

      <!-- 数据区域 - 五芒星形状 -->
      <polygon points="150,60 195,120 180,180 120,180 105,120"
               fill="url(#radarGradient)"
               stroke="#3b82f6"
               stroke-width="2" />

      <!-- 数据点 -->
      <circle cx="150" cy="60" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="195" cy="120" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="180" cy="180" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="120" cy="180" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
      <circle cx="105" cy="120" r="4" fill="#1d4ed8" stroke="#ffffff" stroke-width="2" />
    </svg>
  `
}

// 更新图表数据的方法
const updateChart = () => {
  if (!chartInstance) return

  const option = {
    radar: {
      indicator: props.labels.map(() => ({
        name: '',
        max: props.maxValue
      }))
    },
    series: [{
      data: [{
        value: props.data
      }]
    }]
  }

  chartInstance.setOption(option, { replaceMerge: ['series'] })
}

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(() => props.labels, updateChart, { deep: true })
watch(() => props.maxValue, updateChart)

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  await initChart()
})
</script>
