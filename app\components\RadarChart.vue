<template>
  <div class="relative flex items-center justify-center m-auto h-60 w-60">
    <!-- ECharts 雷达图容器 -->
    <div ref="chartContainer" class="w-full h-full"></div>

    <!-- 中心图片 -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div
           class="flex items-center justify-center w-20 h-20 overflow-hidden bg-white border-white rounded-full shadow-xl border-3">
        <img
             src="@/assets/images/logo.png"
             alt="Profile"
             class="object-cover w-full h-full"
             @error="showFallback = true"
             v-if="!showFallback">
        <!-- 图片加载失败时的回退显示 -->
        <div
             v-else
             class="flex items-center justify-center w-full h-full bg-gradient-to-br from-blue-500 to-blue-600">
          <span class="text-xl font-bold text-white">{{ username.charAt(0).toUpperCase() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props 定义
interface Props {
  username: string
  data?: number[]
  labels?: string[]
  maxValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [1, 1, 8.5, 9.5, 8.5],
  labels: () => ['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'],
  maxValue: 10
})

// 响应式引用
const chartContainer = ref<HTMLElement>()
const showFallback = ref(false)
let chartInstance: any = null

// 动态导入 ECharts
const initChart = async () => {
  try {
    // 动态导入 ECharts
    const echarts = await import('echarts')

    if (!chartContainer.value) return

    // 初始化图表实例
    chartInstance = echarts.init(chartContainer.value)

    // 配置选项
    const option = {
      backgroundColor: 'transparent',
      radar: {
        // 雷达图配置 - 六边形形状
        shape: 'polygon', // 多边形
        splitNumber: 6, // 分割层数
        radius: '60%',
        center: ['50%', '50%'],
        startAngle: 90, // 从顶部开始

        // 坐标轴配置
        axisLine: {
          lineStyle: {
            color: '#cbd5e1',
            width: 1,
            opacity: 0.6
          }
        },

        // 分割线配置
        splitLine: {
          lineStyle: {
            color: '#e2e8f0',
            width: 1,
            opacity: 0.5
          }
        },

        // 分割区域配置
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']
          }
        },

        // 指示器配置 - 6个点对应六边形
        indicator: props.labels.map(() => ({
          name: '', // 不显示标签，我们用自定义的SCAI标签
          max: props.maxValue,
          axisLabel: {
            show: false
          }
        })),

        // 轴标签配置
        axisLabel: {
          show: false
        }
      },

      // 系列数据
      series: [{
        type: 'radar',
        symbol: 'circle',
        symbolSize: 8,

        // 数据配置
        data: [{
          value: props.data,

          // 区域样式
          areaStyle: {
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.8,
              colorStops: [
                { offset: 0, color: 'rgba(59, 130, 246, 0.8)' },
                { offset: 0.7, color: 'rgba(96, 165, 250, 0.6)' },
                { offset: 1, color: 'rgba(147, 197, 253, 0.4)' }
              ]
            }
          },

          // 线条样式
          lineStyle: {
            color: '#2563eb',
            width: 2.5,
            shadowColor: 'rgba(37, 99, 235, 0.3)',
            shadowBlur: 4
          },

          // 数据点样式
          itemStyle: {
            color: '#1e40af',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: 'rgba(30, 64, 175, 0.4)',
            shadowBlur: 6
          }
        }],

        // 动画配置
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }],

      // 全局动画配置
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }

    // 设置配置并渲染
    chartInstance.setOption(option)

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chartInstance?.resize()
    })
    resizeObserver.observe(chartContainer.value)

    // 组件卸载时清理
    onUnmounted(() => {
      resizeObserver.disconnect()
      chartInstance?.dispose()
    })

  } catch (error) {
    console.error('Failed to load ECharts:', error)
    // 如果 ECharts 加载失败，回退到静态 SVG
    await fallbackToStaticSVG()
  }
}

// 回退到静态 SVG 的方法 - 六边形形状
const fallbackToStaticSVG = async () => {
  if (!chartContainer.value) return

  // 创建静态 SVG 作为回退方案 - 六边形形状
  chartContainer.value.innerHTML = `
    <svg class="w-full h-full" viewBox="0 0 300 300">
      <!-- 定义渐变 -->
      <defs>
        <radialGradient id="radarGradient" cx="50%" cy="50%" r="80%">
          <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
          <stop offset="70%" style="stop-color:#60a5fa;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#93c5fd;stop-opacity:0.4" />
        </radialGradient>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#1e40af" flood-opacity="0.3"/>
        </filter>
      </defs>

      <!-- 背景六边形网格 -->
      <g stroke="#e2e8f0" stroke-width="1" fill="none" opacity="0.5">
        <!-- 外层六边形 -->
        <polygon points="150,40 220,90 220,210 150,260 80,210 80,90" />
        <!-- 第三层六边形 -->
        <polygon points="150,65 195,105 195,195 150,235 105,195 105,105" />
        <!-- 第二层六边形 -->
        <polygon points="150,90 170,120 170,180 150,210 130,180 130,120" />
        <!-- 内层六边形 -->
        <polygon points="150,115 155,135 155,165 150,185 145,165 145,135" />

        <!-- 从中心到各顶点的连线 -->
        <line x1="150" y1="150" x2="150" y2="40" />
        <line x1="150" y1="150" x2="220" y2="90" />
        <line x1="150" y1="150" x2="220" y2="210" />
        <line x1="150" y1="150" x2="150" y2="260" />
        <line x1="150" y1="150" x2="80" y2="210" />
        <line x1="150" y1="150" x2="80" y2="90" />
      </g>

      <!-- 数据区域 - 六边形形状 -->
      <polygon points="150,70 200,110 200,190 150,230 100,190 100,110"
               fill="url(#radarGradient)"
               stroke="#2563eb"
               stroke-width="2.5"
               filter="url(#shadow)" />

      <!-- 数据点 -->
      <circle cx="150" cy="70" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="200" cy="110" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="200" cy="190" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="150" cy="230" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="100" cy="190" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="100" cy="110" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
    </svg>
  `
}

// 更新图表数据的方法
const updateChart = () => {
  if (!chartInstance) return

  const option = {
    radar: {
      indicator: props.labels.map(() => ({
        name: '',
        max: props.maxValue
      }))
    },
    series: [{
      data: [{
        value: props.data
      }]
    }]
  }

  chartInstance.setOption(option, { replaceMerge: ['series'] })
}

// 监听数据变化
watch(() => props.data, updateChart, { deep: true })
watch(() => props.labels, updateChart, { deep: true })
watch(() => props.maxValue, updateChart)

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()
  await initChart()
})
</script>
