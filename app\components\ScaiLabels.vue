<template>
  <div class="absolute inset-0 pointer-events-none">
    <!-- 5个SCAI标签，按五角星顶点位置排列 -->
    <div
         v-for="(label, index) in labels"
         :key="index"
         :class="getLabelPositionClass(index)"
         class="absolute px-3 py-1.5 text-sm font-semibold text-gray-700 transition-all duration-300 bg-white border rounded-xl shadow-sm hover:shadow-lg">
      {{ getDisplayText(index) }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  labels?: string[]
  values?: number[]
  showValues?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  labels: () => ['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'],
  values: () => [9.5, 8.5, 8.5, 9.5, 8.5],
  showValues: false
})

// 获取标签位置的CSS类 - 五角星布局
const getLabelPositionClass = (index: number): string => {
  const positions = [
    // 顶点 (12点方向)
    'top-4 left-1/2 transform -translate-x-1/2',
    // 右上 (约2点方向)
    'top-20 right-12 transform -translate-y-1/2',
    // 右下 (约4点方向)
    'bottom-24 right-16 transform translate-x-1/4',
    // 左下 (约8点方向)
    'bottom-24 left-16 transform -translate-x-1/4',
    // 左上 (约10点方向)
    'top-20 left-12 transform -translate-y-1/2'
  ]

  return positions[index] ?? positions[0] ?? 'top-4 left-1/2 transform -translate-x-1/2'
}

// 格式化显示文本
const getDisplayText = (index: number): string => {
  const label = props.labels[index] || 'SCAI'
  if (props.showValues && props.values[index] !== undefined) {
    return `${label}: ${props.values[index]}`
  }
  return label
}
</script>

<style scoped>
/* 添加一些微妙的动画效果 */
.absolute {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 - 针对不同位置的transform */
.absolute:hover {
  z-index: 10;
}

/* 顶点位置的悬停效果 */
.absolute:nth-child(1):hover {
  transform: translateX(-50%) scale(1.1);
}

/* 右上位置的悬停效果 */
.absolute:nth-child(2):hover {
  transform: translateY(-50%) scale(1.1);
}

/* 右下位置的悬停效果 */
.absolute:nth-child(3):hover {
  transform: translateX(25%) scale(1.1);
}

/* 左下位置的悬停效果 */
.absolute:nth-child(4):hover {
  transform: translateX(-25%) scale(1.1);
}

/* 左上位置的悬停效果 */
.absolute:nth-child(5):hover {
  transform: translateY(-50%) scale(1.1);
}

/* 确保变换过渡平滑 */
.transform {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
