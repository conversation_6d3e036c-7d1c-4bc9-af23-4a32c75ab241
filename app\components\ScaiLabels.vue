<template>
  <div class="absolute inset-0 pointer-events-none">
    <!-- 5个SCAI标签，按五芒星顶点位置排列 -->
    <div
         v-for="(label, index) in labels"
         :key="index"
         :class="getLabelPositionClass(index)"
         class="absolute px-4 py-2 text-sm font-semibold text-gray-700 transition-all duration-300 bg-white/90 backdrop-blur-sm border border-gray-200/60 rounded-full shadow-lg hover:shadow-xl hover:border-blue-400 hover:bg-blue-50/80 hover:text-blue-700">
      {{ getDisplayText(index) }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  labels?: string[]
  values?: number[]
  showValues?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  labels: () => ['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'],
  values: () => [9.5, 8.5, 8.5, 9.5, 8.5],
  showValues: false
})

// 获取标签位置的CSS类 - 五芒星布局
const getLabelPositionClass = (index: number): string => {
  const positions = [
    // 顶点 (12点方向)
    'top-2 left-1/2 transform -translate-x-1/2',
    // 右上 (约2点方向，72度)
    'top-16 right-8',
    // 右下 (约4点方向，144度)
    'bottom-20 right-16',
    // 左下 (约8点方向，216度)
    'bottom-20 left-16',
    // 左上 (约10点方向，288度)
    'top-16 left-8'
  ]

  return positions[index] ?? positions[0] ?? 'top-2 left-1/2 transform -translate-x-1/2'
}

// 格式化显示文本
const getDisplayText = (index: number): string => {
  const label = props.labels[index] || 'SCAI'
  if (props.showValues && props.values[index] !== undefined) {
    return `${label}: ${props.values[index]}`
  }
  return label
}
</script>

<style scoped>
/* 添加一些微妙的动画效果 */
.absolute {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

/* 悬停效果 - 针对不同位置的transform */
.absolute:hover {
  z-index: 20;
  animation: pulse 0.6s ease-in-out;
}

/* 顶点位置的悬停效果 */
.absolute:nth-child(1):hover {
  transform: translateX(-50%) scale(1.15) translateY(-2px);
}

/* 右上位置的悬停效果 */
.absolute:nth-child(2):hover {
  transform: scale(1.15) translateY(-2px);
}

/* 右下位置的悬停效果 */
.absolute:nth-child(3):hover {
  transform: scale(1.15) translateY(-2px);
}

/* 左下位置的悬停效果 */
.absolute:nth-child(4):hover {
  transform: scale(1.15) translateY(-2px);
}

/* 左上位置的悬停效果 */
.absolute:nth-child(5):hover {
  transform: scale(1.15) translateY(-2px);
}

/* 确保变换过渡平滑 */
.transform {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 脉冲动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 添加微妙的入场动画 */
.absolute {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.absolute:nth-child(1) {
  animation-delay: 0.1s;
}

.absolute:nth-child(2) {
  animation-delay: 0.2s;
}

.absolute:nth-child(3) {
  animation-delay: 0.3s;
}

.absolute:nth-child(4) {
  animation-delay: 0.4s;
}

.absolute:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
