<template>
  <div class="absolute inset-0 pointer-events-none">
    <!-- 6个SCAI标签，按六边形顶点位置排列 -->
    <div
         v-for="(label, index) in labels"
         :key="index"
         :class="getLabelPositionClass(index)"
         class="absolute px-2 py-1 text-xs font-medium text-gray-700 transition-all duration-300 bg-white border rounded-lg shadow-sm hover:shadow-md">
      {{ label }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  labels?: string[]
  values?: number[]
  showValues?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  labels: () => ['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'],
  values: () => [9.5, 8.5, 8.5, 9.5, 8.5, 8.5],
  showValues: false
})

// 获取标签位置的CSS类
const getLabelPositionClass = (index: number): string => {
  const positions = [
    // 上 (12点方向)
    'top-2 left-1/2 transform -translate-x-1/2',
    // 右上 (2点方向)
    'top-16 right-8',
    // 右下 (4点方向)
    'bottom-16 right-8',
    // 下 (6点方向)
    'bottom-2 left-1/2 transform -translate-x-1/2',
    // 左下 (8点方向)
    'bottom-16 left-8',
    // 左上 (10点方向)
    'top-16 left-8'
  ]

  return positions[index] || positions[0]
}

// 格式化显示文本
const getDisplayText = (index: number): string => {
  const label = props.labels[index] || 'SCAI'
  if (props.showValues && props.values[index] !== undefined) {
    return `${label}: ${props.values[index]}`
  }
  return label
}
</script>

<style scoped>
/* 添加一些微妙的动画效果 */
.absolute {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.absolute:hover {
  transform: scale(1.05);
  z-index: 10;
}

/* 确保变换不影响已有的transform类 */
.transform {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transform:hover {
  transform: translateX(-50%) scale(1.05);
}
</style>
