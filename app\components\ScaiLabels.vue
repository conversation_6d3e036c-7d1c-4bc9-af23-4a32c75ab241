<template>
  <div class="absolute inset-0 pointer-events-none">
    <!-- 5个SCAI标签，按五芒星顶点位置排列 -->
    <div
         v-for="(label, index) in labels"
         :key="index"
         :class="getLabelPositionClass(index)"
         class="absolute px-4 py-2 text-sm font-medium text-gray-600 transition-all duration-300 bg-white border border-gray-200 rounded-full shadow-md hover:shadow-lg hover:border-blue-300">
      {{ getDisplayText(index) }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  labels?: string[]
  values?: number[]
  showValues?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  labels: () => ['SCAI', 'SCAI', 'SCAI', 'SCAI', 'SCAI'],
  values: () => [9.5, 8.5, 8.5, 9.5, 8.5],
  showValues: false
})

// 获取标签位置的CSS类 - 五芒星布局
const getLabelPositionClass = (index: number): string => {
  const positions = [
    // 顶点 (12点方向)
    'top-2 left-1/2 transform -translate-x-1/2',
    // 右上 (约2点方向，72度)
    'top-16 right-8',
    // 右下 (约4点方向，144度)
    'bottom-20 right-16',
    // 左下 (约8点方向，216度)
    'bottom-20 left-16',
    // 左上 (约10点方向，288度)
    'top-16 left-8'
  ]

  return positions[index] ?? positions[0] ?? 'top-2 left-1/2 transform -translate-x-1/2'
}

// 格式化显示文本
const getDisplayText = (index: number): string => {
  const label = props.labels[index] || 'SCAI'
  if (props.showValues && props.values[index] !== undefined) {
    return `${label}: ${props.values[index]}`
  }
  return label
}
</script>

<style scoped>
/* 添加一些微妙的动画效果 */
.absolute {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 - 针对不同位置的transform */
.absolute:hover {
  z-index: 10;
}

/* 顶点位置的悬停效果 */
.absolute:nth-child(1):hover {
  transform: translateX(-50%) scale(1.1);
}

/* 右上位置的悬停效果 */
.absolute:nth-child(2):hover {
  transform: scale(1.1);
}

/* 右下位置的悬停效果 */
.absolute:nth-child(3):hover {
  transform: scale(1.1);
}

/* 左下位置的悬停效果 */
.absolute:nth-child(4):hover {
  transform: scale(1.1);
}

/* 左上位置的悬停效果 */
.absolute:nth-child(5):hover {
  transform: scale(1.1);
}

/* 确保变换过渡平滑 */
.transform {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
